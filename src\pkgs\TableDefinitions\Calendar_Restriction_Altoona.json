{"DataEntityMetadataJson": {"9e990f72-39c6-4240-a3f2-f44765f7363a": "{\"name\":\"9e990f72-39c6-4240-a3f2-f44765f7363a\",\"title\":\"Calendar_Restriction_Erie\",\"x-ms-permission\":\"read-write\",\"x-ms-capabilities\":{\"sortRestrictions\":{\"sortable\":true,\"unsortableProperties\":[\"{Identifier}\",\"{IsFolder}\",\"{Thumbnail}\",\"{Link}\",\"{Name}\",\"{FilenameWithExtension}\",\"{Path}\",\"{FullPath}\",\"{ModerationStatus}\",\"{ModerationComment}\",\"{ContentType}\",\"{HasAttachments}\",\"{Attachments}\",\"{VersionNumber}\",\"{TriggerWindowStartToken}\",\"{TriggerWindowEndToken}\"]},\"filterRestrictions\":{\"filterable\":true,\"nonFilterableProperties\":[\"{Identifier}\",\"{IsFolder}\",\"{Thumbnail}\",\"{<PERSON>}\",\"{Name}\",\"{FilenameWithExtension}\",\"{Path}\",\"{FullPath}\",\"{ModerationStatus}\",\"{ModerationComment}\",\"{ContentType}\",\"{HasAttachments}\",\"{Attachments}\",\"{VersionNumber}\",\"{TriggerWindowStartToken}\",\"{TriggerWindowEndToken}\"]},\"isOnlyServerPagable\":true,\"filterFunctionSupport\":[\"and\",\"or\",\"eq\",\"startswith\",\"gt\",\"ge\",\"lt\",\"le\",\"ne\"],\"serverPagingOptions\":[\"top\",\"skiptoken\"],\"odataVersion\":3},\"schema\":{\"type\":\"array\",\"items\":{\"type\":\"object\",\"required\":[],\"properties\":{\"ID\":{\"title\":\"ID\",\"description\":\"List item id. Use this value for specifying the item to act on in other list related actions.\",\"type\":\"integer\",\"format\":\"int64\",\"x-ms-keyType\":\"primary\",\"x-ms-keyOrder\":1,\"x-ms-permission\":\"read-only\",\"x-ms-sort\":\"asc,desc\",\"x-ms-capabilities\":{\"filterFunctions\":[\"eq\"]}},\"Title\":{\"title\":\"Title\",\"type\":\"string\",\"x-ms-permission\":\"read-write\",\"x-ms-sort\":\"asc,desc\",\"x-ms-capabilities\":{\"filterFunctions\":[\"eq\",\"startswith\"]},\"maxLength\":255},\"Holiday#Id\":{\"title\":\"Events Id\",\"type\":\"integer\",\"format\":\"int64\",\"x-ms-permission\":\"read-only\",\"x-ms-visibility\":\"internal\"},\"Holiday\":{\"title\":\"Events\",\"type\":\"object\",\"x-ms-permission\":\"read-write\",\"x-ms-sort\":\"asc,desc\",\"x-ms-capabilities\":{\"x-ms-sp\":{\"IsChoice\":true},\"filterFunctions\":[\"eq\"]},\"properties\":{\"@odata.type\":{\"title\":\"@odata.type\",\"x-ms-visibility\":\"internal\",\"type\":\"string\",\"x-ms-permission\":\"read-write\"},\"Value\":{\"title\":\"Value\",\"type\":\"string\",\"x-ms-dynamic-values\":{\"operationId\":\"GetEntityValues\",\"parameters\":{\"dataset\":{\"parameter\":\"dataset\"},\"table\":{\"parameter\":\"table\"},\"id\":\"69228750-a09f-4293-af74-c021b34b66c7\"},\"value-path\":\"Value\",\"value-title\":\"Value\",\"value-collection\":\"value\"}},\"Id\":{\"title\":\"Id\",\"type\":\"integer\",\"format\":\"int64\",\"x-ms-visibility\":\"internal\",\"x-ms-permission\":\"read-only\"}},\"x-ms-displayFormat\":{\"titleProperty\":\"Value\"}},\"DatePattern\":{\"title\":\"Date Pattern\",\"type\":\"string\",\"x-ms-permission\":\"read-write\",\"x-ms-sort\":\"asc,desc\",\"x-ms-capabilities\":{\"filterFunctions\":[\"eq\",\"startswith\"]},\"maxLength\":255},\"Date\":{\"title\":\"Date\",\"type\":\"string\",\"format\":\"date\",\"x-ms-permission\":\"read-write\",\"x-ms-sort\":\"asc,desc\",\"x-ms-capabilities\":{\"filterFunctions\":[\"eq\",\"gt\",\"ge\",\"lt\",\"le\",\"ne\"]}},\"TypeofEvent#Id\":{\"title\":\"Type of Event Id\",\"type\":\"integer\",\"format\":\"int64\",\"x-ms-permission\":\"read-only\",\"x-ms-visibility\":\"internal\"},\"TypeofEvent\":{\"title\":\"Type of Event\",\"type\":\"object\",\"x-ms-permission\":\"read-write\",\"x-ms-sort\":\"asc,desc\",\"x-ms-capabilities\":{\"x-ms-sp\":{\"IsChoice\":true},\"filterFunctions\":[\"eq\"]},\"properties\":{\"@odata.type\":{\"title\":\"@odata.type\",\"x-ms-visibility\":\"internal\",\"type\":\"string\",\"x-ms-permission\":\"read-write\"},\"Value\":{\"title\":\"Value\",\"type\":\"string\",\"x-ms-dynamic-values\":{\"operationId\":\"GetEntityValues\",\"parameters\":{\"dataset\":{\"parameter\":\"dataset\"},\"table\":{\"parameter\":\"table\"},\"id\":\"964c6adf-2063-4440-ae8d-fa7b176f2fb9\"},\"value-path\":\"Value\",\"value-title\":\"Value\",\"value-collection\":\"value\"}},\"Id\":{\"title\":\"Id\",\"type\":\"integer\",\"format\":\"int64\",\"x-ms-visibility\":\"internal\",\"x-ms-permission\":\"read-only\"}},\"x-ms-displayFormat\":{\"titleProperty\":\"Value\"}},\"OData__ColorTag\":{\"title\":\"Color Tag\",\"type\":\"string\",\"x-ms-permission\":\"read-only\",\"x-ms-sort\":\"asc,desc\",\"x-ms-capabilities\":{\"filterFunctions\":[\"eq\",\"startswith\"]},\"maxLength\":255},\"ComplianceAssetId\":{\"title\":\"Compliance Asset Id\",\"type\":\"string\",\"x-ms-permission\":\"read-only\",\"x-ms-sort\":\"asc,desc\",\"x-ms-capabilities\":{\"filterFunctions\":[\"eq\",\"startswith\"]},\"maxLength\":255},\"Modified\":{\"title\":\"Modified\",\"description\":\"When this item was last changed.\",\"type\":\"string\",\"format\":\"date-time\",\"x-ms-permission\":\"read-only\",\"x-ms-sort\":\"asc,desc\",\"x-ms-capabilities\":{\"filterFunctions\":[\"eq\",\"gt\",\"ge\",\"lt\",\"le\",\"ne\"]}},\"Created\":{\"title\":\"Created\",\"description\":\"When this item was created.\",\"type\":\"string\",\"format\":\"date-time\",\"x-ms-permission\":\"read-only\",\"x-ms-sort\":\"asc,desc\",\"x-ms-capabilities\":{\"filterFunctions\":[\"eq\",\"gt\",\"ge\",\"lt\",\"le\",\"ne\"]}},\"Author#Claims\":{\"title\":\"Created By Claims\",\"type\":\"string\",\"description\":\"Internal identifier for the user who created this item.\",\"x-ms-permission\":\"read-only\",\"x-ms-visibility\":\"internal\"},\"Author\":{\"title\":\"Created By\",\"description\":\"The user who created this item.\",\"type\":\"object\",\"x-ms-permission\":\"read-only\",\"x-ms-sort\":\"asc,desc\",\"x-ms-capabilities\":{\"filterFunctions\":[\"eq\"]},\"properties\":{\"@odata.type\":{\"title\":\"@odata.type\",\"x-ms-visibility\":\"internal\",\"type\":\"string\",\"x-ms-permission\":\"read-only\"},\"Claims\":{\"title\":\"Claims\",\"type\":\"string\",\"description\":\"Internal identifier for the user who created this item.\",\"x-ms-permission\":\"read-only\",\"x-ms-dynamic-values\":{\"operationId\":\"GetEntityValues\",\"parameters\":{\"dataset\":{\"parameter\":\"dataset\"},\"table\":{\"parameter\":\"table\"},\"id\":\"1df5e554-ec7e-46a6-901d-d85a3881cb18\"},\"value-path\":\"Claims\",\"value-title\":\"DisplayName\",\"value-collection\":\"value\"}},\"DisplayName\":{\"title\":\"DisplayName\",\"type\":\"string\",\"description\":\"The name of the user who created this item.\",\"x-ms-permission\":\"read-only\",\"x-ms-capabilities\":{\"x-ms-sp\":{\"OdataQueryName\":\"Title\"},\"filterFunctions\":[\"eq\",\"startswith\"]}},\"Email\":{\"title\":\"Email\",\"type\":\"string\",\"description\":\"Email address of the user who created this item.\",\"format\":\"email\",\"x-ms-permission\":\"read-only\",\"x-ms-capabilities\":{\"x-ms-sp\":{\"OdataQueryName\":\"EMail\"},\"filterFunctions\":[\"eq\",\"startswith\"]}},\"Picture\":{\"title\":\"Picture\",\"type\":\"string\",\"description\":\"Link to a picture of the user who created this item.\",\"format\":\"uri\",\"x-ms-media-kind\":\"image\",\"x-ms-media-default-folder-path\":\"/sites/GRP-CentralOfficeCustomerService-OfficeOfAdministration-StatisticsUnit\",\"x-ms-media-base-url\":\"https://pagov.sharepoint.com/sites/GRP-CentralOfficeCustomerService-OfficeOfAdministration-StatisticsUnit\",\"x-ms-permission\":\"read-only\"},\"Department\":{\"title\":\"Department\",\"type\":\"string\",\"description\":\"Department name of the user who created this item.\",\"x-ms-permission\":\"read-only\"},\"JobTitle\":{\"title\":\"JobTitle\",\"type\":\"string\",\"description\":\"Job title of the user who created this item.\",\"x-ms-permission\":\"read-only\"}},\"x-ms-displayFormat\":{\"titleProperty\":\"DisplayName\",\"subtitleProperty\":\"Email\",\"thumbnailProperty\":\"Picture\"}},\"Editor#Claims\":{\"title\":\"Modified By Claims\",\"type\":\"string\",\"description\":\"Internal identifier for the user who last changed this item.\",\"x-ms-permission\":\"read-only\",\"x-ms-visibility\":\"internal\"},\"Editor\":{\"title\":\"Modified By\",\"description\":\"The user who last changed this item.\",\"type\":\"object\",\"x-ms-permission\":\"read-only\",\"x-ms-sort\":\"asc,desc\",\"x-ms-capabilities\":{\"filterFunctions\":[\"eq\"]},\"properties\":{\"@odata.type\":{\"title\":\"@odata.type\",\"x-ms-visibility\":\"internal\",\"type\":\"string\",\"x-ms-permission\":\"read-only\"},\"Claims\":{\"title\":\"Claims\",\"type\":\"string\",\"description\":\"Internal identifier for the user who last changed this item.\",\"x-ms-permission\":\"read-only\",\"x-ms-dynamic-values\":{\"operationId\":\"GetEntityValues\",\"parameters\":{\"dataset\":{\"parameter\":\"dataset\"},\"table\":{\"parameter\":\"table\"},\"id\":\"d31655d1-1d5b-4511-95a1-7a09e9b75bf2\"},\"value-path\":\"Claims\",\"value-title\":\"DisplayName\",\"value-collection\":\"value\"}},\"DisplayName\":{\"title\":\"DisplayName\",\"type\":\"string\",\"description\":\"The name of the user who last changed this item.\",\"x-ms-permission\":\"read-only\",\"x-ms-capabilities\":{\"x-ms-sp\":{\"OdataQueryName\":\"Title\"},\"filterFunctions\":[\"eq\",\"startswith\"]}},\"Email\":{\"title\":\"Email\",\"type\":\"string\",\"description\":\"Email address of the user who last changed this item.\",\"format\":\"email\",\"x-ms-permission\":\"read-only\",\"x-ms-capabilities\":{\"x-ms-sp\":{\"OdataQueryName\":\"EMail\"},\"filterFunctions\":[\"eq\",\"startswith\"]}},\"Picture\":{\"title\":\"Picture\",\"type\":\"string\",\"description\":\"Link to a picture of the user who last changed this item.\",\"format\":\"uri\",\"x-ms-media-kind\":\"image\",\"x-ms-media-default-folder-path\":\"/sites/GRP-CentralOfficeCustomerService-OfficeOfAdministration-StatisticsUnit\",\"x-ms-media-base-url\":\"https://pagov.sharepoint.com/sites/GRP-CentralOfficeCustomerService-OfficeOfAdministration-StatisticsUnit\",\"x-ms-permission\":\"read-only\"},\"Department\":{\"title\":\"Department\",\"type\":\"string\",\"description\":\"Department name of the user who last changed this item.\",\"x-ms-permission\":\"read-only\"},\"JobTitle\":{\"title\":\"JobTitle\",\"type\":\"string\",\"description\":\"Job title of the user who last changed this item.\",\"x-ms-permission\":\"read-only\"}},\"x-ms-displayFormat\":{\"titleProperty\":\"DisplayName\",\"subtitleProperty\":\"Email\",\"thumbnailProperty\":\"Picture\"}},\"{Identifier}\":{\"title\":\"Identifier\",\"description\":\"Value that can be used in file related actions for selecting a file.\",\"type\":\"string\",\"x-ms-permission\":\"read-only\",\"x-ms-sort\":\"none\"},\"{IsFolder}\":{\"title\":\"IsFolder\",\"description\":\"True when the item is a folder, false otherwise.\",\"type\":\"boolean\",\"x-ms-permission\":\"read-only\",\"x-ms-sort\":\"none\"},\"{Thumbnail}\":{\"title\":\"Thumbnail\",\"description\":\"URL to the thumbnails of the item in 3 different sizes, if available.\",\"type\":\"object\",\"x-ms-permission\":\"read-only\",\"x-ms-sort\":\"none\",\"properties\":{\"@odata.type\":{\"title\":\"@odata.type\",\"x-ms-visibility\":\"internal\",\"type\":\"string\",\"x-ms-permission\":\"read-only\"},\"Small\":{\"title\":\"Small\",\"type\":\"string\",\"description\":\"URL to the small, highly compressed size thumbnail of the item, if available.\",\"x-ms-permission\":\"read-only\"},\"Medium\":{\"title\":\"Medium\",\"type\":\"string\",\"description\":\"URL to the standard size thumbnail of the item, if available.\",\"x-ms-permission\":\"read-only\"},\"Large\":{\"title\":\"Large\",\"type\":\"string\",\"description\":\"URL to the largest size thumbnail of the item, if available.\",\"x-ms-permission\":\"read-only\"}}},\"{Link}\":{\"title\":\"Link to item\",\"description\":\"Link that can be used to get to the file or list item. Only people with permissions to the item will be able to open the link.\",\"type\":\"string\",\"x-ms-permission\":\"read-only\",\"x-ms-sort\":\"none\"},\"{Name}\":{\"title\":\"Name\",\"description\":\"File name of the item in document libraries, display name of the item in lists.\",\"type\":\"string\",\"x-ms-permission\":\"read-only\",\"x-ms-sort\":\"none\"},\"{FilenameWithExtension}\":{\"title\":\"File name with extension\",\"description\":\"For libraries, returns file name including extension. For lists, returns the title property.\",\"type\":\"string\",\"x-ms-permission\":\"read-only\",\"x-ms-sort\":\"none\"},\"{Path}\":{\"title\":\"Folder path\",\"description\":\"Path to the folder the item is in, relative to the site address.\",\"type\":\"string\",\"x-ms-permission\":\"read-only\",\"x-ms-sort\":\"none\"},\"{FullPath}\":{\"title\":\"Full Path\",\"description\":\"Full path of an item or a folder or a file\",\"type\":\"string\",\"x-ms-permission\":\"read-only\",\"x-ms-sort\":\"none\"},\"{ModerationStatus}\":{\"title\":\"Content approval status\",\"description\":\"The content approval status. It can be one of the following: Draft, Pending, Approved, or Rejected\",\"type\":\"string\",\"x-ms-permission\":\"read-only\",\"x-ms-sort\":\"none\"},\"{ModerationComment}\":{\"title\":\"Comments associated with the content approval of this list item\",\"description\":\"The comments associated with moderation of the list item.\",\"type\":\"string\",\"x-ms-permission\":\"read-only\",\"x-ms-sort\":\"none\"},\"{ContentType}#Id\":{\"title\":\"Content type Id\",\"type\":\"string\",\"x-ms-permission\":\"read-only\",\"x-ms-visibility\":\"internal\"},\"{ContentType}\":{\"title\":\"Content type\",\"description\":\"Name of the Content type of the item.\",\"type\":\"object\",\"x-ms-permission\":\"read-write\",\"x-ms-sort\":\"none\",\"properties\":{\"@odata.type\":{\"title\":\"@odata.type\",\"x-ms-visibility\":\"internal\",\"type\":\"string\",\"x-ms-permission\":\"read-write\"},\"Id\":{\"title\":\"Id\",\"type\":\"string\",\"x-ms-dynamic-values\":{\"operationId\":\"GetEntityValues\",\"parameters\":{\"dataset\":{\"parameter\":\"dataset\"},\"table\":{\"parameter\":\"table\"},\"id\":\"4651e8f2-38c9-4ad0-8def-41f743f76f30\"},\"value-path\":\"Id\",\"value-title\":\"Name\",\"value-collection\":\"value\"}},\"Name\":{\"title\":\"Name\",\"type\":\"string\",\"x-ms-permission\":\"read-only\"}},\"x-ms-displayFormat\":{\"titleProperty\":\"Name\"}},\"{HasAttachments}\":{\"title\":\"Has attachments\",\"description\":\"Indicates the presence of attachments in the item.\",\"type\":\"boolean\",\"x-ms-permission\":\"read-only\",\"x-ms-sort\":\"none\"},\"{Attachments}@odata.type\":{\"title\":\"{Attachments}\",\"type\":\"string\",\"x-ms-visibility\":\"internal\",\"x-ms-permission\":\"read-write\"},\"{Attachments}\":{\"title\":\"Attachments\",\"description\":\"Never returns any data. Use Attachment endpoints to access item attachments.\",\"x-ms-permission\":\"read-write\",\"targetEntity\":\"attachments\",\"format\":\"x-ms-containedMediaEntity\",\"x-ms-visibility\":\"internal\",\"type\":\"array\",\"items\":{\"type\":\"object\",\"format\":\"x-ms-mediaEntity\",\"x-ms-sort\":\"none\",\"properties\":{\"@odata.type\":{\"title\":\"@odata.type\",\"x-ms-visibility\":\"internal\",\"type\":\"string\",\"x-ms-permission\":\"read-write\"},\"Id\":{\"title\":\"Id\",\"type\":\"string\",\"x-ms-keyType\":\"primary\",\"x-ms-keyOrder\":1,\"x-ms-permission\":\"read-only\"},\"AbsoluteUri\":{\"title\":\"AbsoluteUri\",\"type\":\"string\",\"format\":\"uri\",\"x-ms-permission\":\"read-only\"},\"DisplayName\":{\"title\":\"DisplayName\",\"type\":\"string\",\"x-ms-permission\":\"read-only\"}}},\"@odata.type\":\"#Collection(Microsoft.Azure.Connectors.SharePoint.SPListItemAttachment)\"},\"{VersionNumber}\":{\"title\":\"Version number\",\"description\":\"The version number of the file or the list item.\",\"type\":\"string\",\"x-ms-permission\":\"read-only\",\"x-ms-sort\":\"none\"},\"{TriggerWindowStartToken}\":{\"title\":\"Trigger Window Start Token\",\"description\":\"A token expressing the time of the last flow check. Use this if you want to check if one or more columns were modified since the last flow check.\",\"type\":\"string\",\"x-ms-permission\":\"read-only\",\"x-ms-sort\":\"none\"},\"{TriggerWindowEndToken}\":{\"title\":\"Trigger Window End Token\",\"description\":\"A token expressing the time of the current flow check. Use this if you want to check if one or more columns were modified since the last flow check.\",\"type\":\"string\",\"x-ms-permission\":\"read-only\",\"x-ms-sort\":\"none\"}},\"x-ms-relationships\":{\"Holiday\":{\"targetEntity\":\"Holiday\",\"referentialConstraints\":{\"Holiday#Id\":{\"referencedProperty\":\"Id\"}}},\"TypeofEvent\":{\"targetEntity\":\"TypeofEvent\",\"referentialConstraints\":{\"TypeofEvent#Id\":{\"referencedProperty\":\"Id\"}}},\"Author\":{\"targetEntity\":\"Author\",\"referentialConstraints\":{\"Author#Claims\":{\"referencedProperty\":\"Claims\"}}},\"Editor\":{\"targetEntity\":\"Editor\",\"referentialConstraints\":{\"Editor#Claims\":{\"referencedProperty\":\"Claims\"}}},\"{ContentType}\":{\"targetEntity\":\"4651e8f2-38c9-4ad0-8def-41f743f76f30\",\"referentialConstraints\":{\"{ContentType}#Id\":{\"referencedProperty\":\"Id\"}}}},\"x-ms-displayFormat\":{\"propertiesDisplayOrder\":[\"Title\",\"Holiday\",\"DatePattern\",\"Date\",\"TypeofEvent\",\"{Attachments}\"],\"propertiesCompactDisplayOrder\":[\"Title\",\"Holiday\",\"DatePattern\",\"Date\",\"TypeofEvent\"],\"propertiesTabularDisplayOrder\":[\"Title\",\"Holiday\",\"DatePattern\",\"Date\",\"TypeofEvent\"]}}},\"referencedEntities\":{\"Holiday\":{\"lookupEndpoint\":{\"path\":\"/tables/9e990f72-39c6-4240-a3f2-f44765f7363a/entities/Holiday\",\"capabilities\":{\"searchable\":true,\"requiresSearch\":false}}},\"TypeofEvent\":{\"lookupEndpoint\":{\"path\":\"/tables/9e990f72-39c6-4240-a3f2-f44765f7363a/entities/TypeofEvent\",\"capabilities\":{\"searchable\":true,\"requiresSearch\":false}}},\"Author\":{\"lookupEndpoint\":{\"path\":\"/tables/9e990f72-39c6-4240-a3f2-f44765f7363a/entities/Author\",\"capabilities\":{\"searchable\":true,\"requiresSearch\":false}}},\"Editor\":{\"lookupEndpoint\":{\"path\":\"/tables/9e990f72-39c6-4240-a3f2-f44765f7363a/entities/Editor\",\"capabilities\":{\"searchable\":true,\"requiresSearch\":false}}},\"4651e8f2-38c9-4ad0-8def-41f743f76f30\":{\"lookupEndpoint\":{\"path\":\"/tables/9e990f72-39c6-4240-a3f2-f44765f7363a/entities/%257bContentType%257d\",\"capabilities\":{\"searchable\":true,\"requiresSearch\":false}}}},\"webUrl\":\"https://pagov.sharepoint.com/sites/GRP-CentralOfficeCustomerService-OfficeOfAdministration-StatisticsUnit/Lists/Calender_Restriction_Erie/AllItems.aspx\"}"}, "EntityName": "Calendar_Restriction_Erie", "TableName": "9e990f72-39c6-4240-a3f2-f44765f7363a"}